# Tree-py: Functional Programming Tree Command

A production-quality functional programming implementation of the classic `tree` command using Python and the Expression library. This project demonstrates how to build robust, maintainable software using functional programming principles.

## 🎯 Project Goals

This project showcases:
- **Pure Functional Programming**: No classes, methods, or mutable state
- **Immutable Data Structures**: Using Expression library's immutable collections
- **Railway-Oriented Programming**: Robust error handling with Result types
- **Function Composition**: Building complex operations from simple functions
- **Type Safety**: Leveraging Python's type system and Expression's tagged unions

## 🏗️ Functional Architecture

### Core Principles Applied

1. **No Object-Oriented Programming**
   - No classes, methods, or inheritance
   - Only pure functions and immutable data structures
   - Function composition instead of object composition

2. **Immutable Data Structures**
   - All data types are frozen dataclasses
   - Using Expression's `Block` (immutable list) and `Map` (immutable dict)
   - No in-place modifications, only transformations

3. **Railway-Oriented Programming**
   - Using `Result[T, Error]` types for error handling
   - No exceptions for control flow
   - Composable error handling with `bind` and `map`

4. **Function Composition**
   - Building complex operations by composing simple functions
   - Using Expression's `pipe` and `compose` functions
   - Declarative data transformation pipelines

### Module Structure

```
tree_py/
├── core/
│   ├── types.py          # Immutable data types and tagged unions
│   ├── filesystem.py     # Pure filesystem operations with Result types
│   ├── tree_builder.py   # Tree construction using function composition
│   └── formatters.py     # Output formatting with pure functions
└── main.py              # CLI interface using functional pipeline
```

## 🔧 Key Functional Programming Features

### Tagged Unions for Type Safety

```python
@tagged_union
class TreeError:
    tag: Literal["permission_denied", "not_found", "io_error"] = tag()
    
    permission_denied: str = case()
    not_found: str = case()
    io_error: str = case()
```

### Result Types for Error Handling

```python
def safe_stat(path: Path) -> Result[os.stat_result, TreeError]:
    try:
        return Ok(path.stat())
    except PermissionError:
        return Error(TreeError.PermissionDenied(str(path)))
```

### Immutable Collections

```python
def filter_files(files: Block[FileInfo], config: TreeConfig) -> Block[FileInfo]:
    return pipe(
        files,
        lambda fs: fs.filter(lambda f: should_include_file(f, config))
    )
```

### Function Composition

```python
main_pipeline = compose(
    parse_arguments,
    lambda args: (args.path, create_config_from_args(args)),
    lambda path_config: validate_path(path_config[0]).bind(
        lambda valid_path: run_tree_command(valid_path, path_config[1])
    )
)
```

## 🚀 Installation and Usage

### Prerequisites

- Python 3.11+
- uv package manager

### Setup

```bash
# Clone and setup
git clone <repository>
cd tree-py

# Create virtual environment
uv venv

# Activate virtual environment
.venv\Scripts\activate  # Windows
# or
source .venv/bin/activate  # Linux/Mac

# Install dependencies
uv add expression
```

### Usage

```bash
# Basic usage
python main.py

# Show hidden files with sizes
python main.py -a -s

# Limit depth and show permissions
python main.py -L 3 -p

# JSON output with detailed info
python main.py -J -s -p -D

# XML output
python main.py -X

# HTML output
python main.py -H
```

### Command Line Options

- `-L, --max-depth`: Maximum depth to display
- `-a, --all`: Show hidden files and directories
- `-s, --size`: Show file sizes
- `-p, --permissions`: Show file permissions
- `-D, --date`: Show modification dates
- `-r, --reverse`: Reverse sort order
- `--sort`: Sort by name, size, or date
- `-J, --json`: Output in JSON format
- `-X, --xml`: Output in XML format
- `-H, --html`: Output in HTML format
- `-I, --include`: Include files matching pattern
- `--exclude`: Exclude files matching pattern
- `-l, --follow-links`: Follow symbolic links
- `-n, --no-color`: Disable colored output

## 🧪 Testing

Run the comprehensive functional programming tests:

```bash
python test_tree_py.py
```

The tests verify:
- Immutability of data structures
- Tagged union pattern matching
- Result type error handling
- Function composition
- Filesystem operations
- Tree building and formatting

## 📚 Functional Programming Concepts Demonstrated

### 1. Pure Functions
All functions are pure - they don't modify external state and always return the same output for the same input.

### 2. Immutable Data
All data structures are immutable, preventing accidental modifications and making the code more predictable.

### 3. Monadic Error Handling
Using Result types to handle errors without exceptions, enabling composable error handling.

### 4. Function Composition
Building complex operations by composing simple functions, making the code more modular and testable.

### 5. Type Safety
Using Python's type system and Expression's tagged unions to catch errors at compile time.

## 🔍 Comparison with Original Tree Command

### Advantages of Functional Approach

1. **Predictability**: Pure functions make behavior predictable
2. **Testability**: Easy to test individual functions in isolation
3. **Composability**: Functions can be easily combined and reused
4. **Error Handling**: Explicit error handling with Result types
5. **Immutability**: No accidental state modifications
6. **Type Safety**: Compile-time error detection

### Performance Considerations

While functional programming can have some overhead due to immutable data structures, the benefits in maintainability and correctness often outweigh the performance costs for most applications.

## 🛠️ Expression Library Features Used

- **Result Types**: For error handling without exceptions
- **Option Types**: For handling optional values
- **Immutable Collections**: Block, Map for immutable data structures
- **Tagged Unions**: For type-safe discriminated unions
- **Function Composition**: pipe, compose for building pipelines
- **Pattern Matching**: For handling different cases safely

## 📖 Learning Resources

- [Expression Library Documentation](https://expression.readthedocs.io/)
- [Functional Programming in Python](https://docs.python.org/3/howto/functional.html)
- [Railway Oriented Programming](https://fsharpforfunandprofit.com/rop/)
- [F# for Fun and Profit](https://fsharpforfunandprofit.com/)

## 🤝 Contributing

This project serves as an educational example of functional programming in Python. Contributions that further demonstrate functional programming concepts are welcome!

## 📄 License

MIT License - see LICENSE file for details.
