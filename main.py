"""
Main CLI interface for tree-py: A functional tree command implementation.

This module provides the command-line interface using functional programming
principles and the Expression library for robust error handling.
"""

import argparse
import sys
from pathlib import Path
from typing import Optional

from expression import Result, Ok, Error, pipe, compose
from expression.collections import Block

from tree_py.core.types import TreeConfig, OutputFormat, TreeError
from tree_py.core.tree_builder import build_tree, calculate_tree_stats
from tree_py.core.formatters import format_tree


def parse_arguments() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="A functional programming implementation of the tree command"
    )
    
    parser.add_argument(
        "path",
        nargs="?",
        default=".",
        help="Directory path to display (default: current directory)"
    )
    
    parser.add_argument(
        "-L", "--max-depth",
        type=int,
        help="Maximum depth of directory tree to display"
    )
    
    parser.add_argument(
        "-a", "--all",
        action="store_true",
        help="Show hidden files and directories"
    )
    
    parser.add_argument(
        "-s", "--size",
        action="store_true",
        help="Show file sizes"
    )
    
    parser.add_argument(
        "-p", "--permissions",
        action="store_true",
        help="Show file permissions"
    )
    
    parser.add_argument(
        "-D", "--date",
        action="store_true",
        help="Show modification dates"
    )
    
    parser.add_argument(
        "-r", "--reverse",
        action="store_true",
        help="Reverse sort order"
    )
    
    parser.add_argument(
        "--sort",
        choices=["name", "size", "date"],
        default="name",
        help="Sort files by specified criteria"
    )
    
    parser.add_argument(
        "-J", "--json",
        action="store_true",
        help="Output in JSON format"
    )
    
    parser.add_argument(
        "-X", "--xml",
        action="store_true",
        help="Output in XML format"
    )
    
    parser.add_argument(
        "-H", "--html",
        action="store_true",
        help="Output in HTML format"
    )
    
    parser.add_argument(
        "-I", "--include",
        action="append",
        help="Include only files matching pattern"
    )
    
    parser.add_argument(
        "--exclude",
        action="append", 
        help="Exclude files matching pattern"
    )
    
    parser.add_argument(
        "-l", "--follow-links",
        action="store_true",
        help="Follow symbolic links"
    )
    
    parser.add_argument(
        "-n", "--no-color",
        action="store_true",
        help="Disable colored output"
    )
    
    return parser.parse_args()


def create_config_from_args(args: argparse.Namespace) -> TreeConfig:
    """Create TreeConfig from parsed arguments."""
    # Determine output format
    output_format = OutputFormat.Text()
    if args.json:
        output_format = OutputFormat.JSON()
    elif args.xml:
        output_format = OutputFormat.XML()
    elif args.html:
        output_format = OutputFormat.HTML()
    
    # Create pattern blocks
    include_patterns = Block.of_seq(args.include or [])
    exclude_patterns = Block.of_seq(args.exclude or [])
    
    return TreeConfig(
        max_depth=args.max_depth,
        show_hidden=args.all,
        show_size=args.size,
        show_permissions=args.permissions,
        show_date=args.date,
        sort_by=args.sort,
        reverse_sort=args.reverse,
        output_format=output_format,
        include_patterns=include_patterns,
        exclude_patterns=exclude_patterns,
        follow_symlinks=args.follow_links,
        color_output=not args.no_color
    )


def validate_path(path_str: str) -> Result[Path, TreeError]:
    """Validate that the given path exists and is accessible."""
    try:
        path = Path(path_str).resolve()
        if not path.exists():
            return Error(TreeError.NotFound(str(path)))
        return Ok(path)
    except (OSError, ValueError) as e:
        return Error(TreeError.InvalidPath(f"{path_str}: {e}"))


def handle_tree_error(error: TreeError) -> str:
    """Convert TreeError to user-friendly error message."""
    match error:
        case TreeError(tag="permission_denied", permission_denied=msg):
            return f"Permission denied: {msg}"
        case TreeError(tag="not_found", not_found=msg):
            return f"Path not found: {msg}"
        case TreeError(tag="io_error", io_error=msg):
            return f"IO error: {msg}"
        case TreeError(tag="invalid_path", invalid_path=msg):
            return f"Invalid path: {msg}"
        case _:
            return f"Unknown error: {error}"


def run_tree_command(path: Path, config: TreeConfig) -> Result[str, TreeError]:
    """Run the tree command with functional composition."""
    return pipe(
        build_tree(path, config),
        lambda tree_result: tree_result.map(
            lambda tree: (tree, calculate_tree_stats(tree))
        ),
        lambda result: result.map(
            lambda tree_stats: format_tree(tree_stats[0], config, tree_stats[1])
        )
    )


# Main pipeline composition
main_pipeline = compose(
    parse_arguments,
    lambda args: (args.path, create_config_from_args(args)),
    lambda path_config: validate_path(path_config[0]).bind(
        lambda valid_path: run_tree_command(valid_path, path_config[1])
    )
)


def main() -> int:
    """Main entry point using functional pipeline."""
    try:
        result = main_pipeline()
        
        match result:
            case Ok(output):
                print(output)
                return 0
            case Error(error):
                print(f"Error: {handle_tree_error(error)}", file=sys.stderr)
                return 1
    except KeyboardInterrupt:
        print("\nInterrupted by user", file=sys.stderr)
        return 130
    except Exception as e:
        print(f"Unexpected error: {e}", file=sys.stderr)
        return 1


if __name__ == "__main__":
    sys.exit(main())
