"""
Comprehensive tests for tree-py functional implementation.

This module tests the functional programming approach using Expression library
features like Result types, immutable collections, and function composition.
"""

import tempfile
import os
from pathlib import Path
from datetime import datetime

from expression import Ok, Error
from expression.collections import Block

from tree_py.core.types import (
    FileInfo, TreeNode, TreeError, TreeConfig, OutputFormat, TreeStats
)
from tree_py.core.filesystem import (
    safe_stat, safe_listdir, get_file_info, filter_files, sort_files
)
from tree_py.core.tree_builder import (
    build_tree, calculate_tree_stats, build_tree_node
)
from tree_py.core.formatters import (
    format_tree, format_file_size, format_text_tree
)


def test_file_info_immutability():
    """Test that FileInfo is immutable."""
    print("Testing FileInfo immutability...")
    
    file_info = FileInfo(
        name="test.txt",
        path=Path("test.txt"),
        size=100,
        modified_time=datetime.now(),
        is_directory=False,
        is_symlink=False,
        permissions="rw-r--r--"
    )
    
    # This should work - accessing attributes
    assert file_info.name == "test.txt"
    assert file_info.size == 100
    
    # This would fail if we tried to modify (frozen=True in dataclass)
    try:
        file_info.name = "modified.txt"  # This should raise an error
        assert False, "FileInfo should be immutable"
    except AttributeError:
        print("✓ FileInfo is properly immutable")


def test_tree_error_tagged_union():
    """Test TreeError tagged union functionality."""
    print("Testing TreeError tagged union...")
    
    # Test different error types
    perm_error = TreeError.PermissionDenied("/restricted/path")
    not_found_error = TreeError.NotFound("/missing/path")
    io_error = TreeError.IOError("/some/path", "Disk full")
    
    # Test pattern matching
    def handle_error(error: TreeError) -> str:
        match error:
            case TreeError(tag="permission_denied", permission_denied=msg):
                return f"Access denied: {msg}"
            case TreeError(tag="not_found", not_found=msg):
                return f"Missing: {msg}"
            case TreeError(tag="io_error", io_error=msg):
                return f"IO problem: {msg}"
            case _:
                return "Unknown error"
    
    assert "Access denied" in handle_error(perm_error)
    assert "Missing" in handle_error(not_found_error)
    assert "IO problem" in handle_error(io_error)
    
    print("✓ TreeError tagged union works correctly")


def test_result_types():
    """Test Result types for error handling."""
    print("Testing Result types...")
    
    # Test successful operation
    success_result = Ok("success")
    assert success_result.is_ok()
    assert not success_result.is_error()
    
    # Test error operation
    error_result = Error(TreeError.NotFound("test"))
    assert error_result.is_error()
    assert not error_result.is_ok()
    
    # Test mapping over Result
    mapped_result = success_result.map(lambda x: x.upper())
    match mapped_result:
        case Ok(value):
            assert value == "SUCCESS"
        case Error(_):
            assert False, "Should be Ok"
    
    print("✓ Result types work correctly")


def test_immutable_collections():
    """Test immutable collections from Expression."""
    print("Testing immutable collections...")
    
    # Test Block (immutable list)
    block = Block.of_seq([1, 2, 3, 4, 5])
    
    # Test filtering
    filtered = block.filter(lambda x: x > 3)
    assert list(filtered) == [4, 5]
    
    # Test mapping
    mapped = block.map(lambda x: x * 2)
    assert list(mapped) == [2, 4, 6, 8, 10]
    
    # Original block unchanged
    assert list(block) == [1, 2, 3, 4, 5]
    
    print("✓ Immutable collections work correctly")


def test_filesystem_operations():
    """Test filesystem operations with Result types."""
    print("Testing filesystem operations...")
    
    # Create a temporary directory structure
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create test files
        (temp_path / "file1.txt").write_text("content1")
        (temp_path / "file2.txt").write_text("content2")
        
        # Create subdirectory
        sub_dir = temp_path / "subdir"
        sub_dir.mkdir()
        (sub_dir / "file3.txt").write_text("content3")
        
        # Test safe_listdir
        result = safe_listdir(temp_path)
        match result:
            case Ok(entries):
                assert len(entries) >= 3  # At least our created files
                print("✓ safe_listdir works correctly")
            case Error(error):
                assert False, f"Unexpected error: {error}"
        
        # Test get_file_info
        file_result = get_file_info(temp_path / "file1.txt")
        match file_result:
            case Ok(file_info):
                assert file_info.name == "file1.txt"
                assert not file_info.is_directory
                print("✓ get_file_info works correctly")
            case Error(error):
                assert False, f"Unexpected error: {error}"


def test_tree_building():
    """Test tree building functionality."""
    print("Testing tree building...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create test structure
        (temp_path / "file1.txt").write_text("content1")
        (temp_path / "file2.txt").write_text("content2")
        
        sub_dir = temp_path / "subdir"
        sub_dir.mkdir()
        (sub_dir / "file3.txt").write_text("content3")
        
        # Build tree
        config = TreeConfig(max_depth=2)
        result = build_tree(temp_path, config)
        
        match result:
            case Ok(tree):
                assert tree.info.is_directory
                assert len(tree.children) >= 3
                print("✓ Tree building works correctly")
            case Error(error):
                assert False, f"Tree building failed: {error}"


def test_formatting():
    """Test different output formats."""
    print("Testing formatting...")
    
    # Create a simple tree structure
    file_info = FileInfo(
        name="test_dir",
        path=Path("test_dir"),
        size=0,
        modified_time=datetime.now(),
        is_directory=True,
        is_symlink=False,
        permissions="drwxr-xr-x"
    )
    
    child_info = FileInfo(
        name="test.txt",
        path=Path("test_dir/test.txt"),
        size=100,
        modified_time=datetime.now(),
        is_directory=False,
        is_symlink=False,
        permissions="-rw-r--r--"
    )
    
    child_node = build_tree_node(child_info, Block.empty(), 1, True)
    tree = build_tree_node(file_info, Block.of_seq([child_node]), 0, True)
    
    stats = TreeStats(total_directories=1, total_files=1, total_size=100)
    
    # Test text formatting
    config = TreeConfig(output_format=OutputFormat.Text())
    text_output = format_tree(tree, config, stats)
    assert "test_dir" in text_output
    assert "test.txt" in text_output
    
    # Test JSON formatting
    config_json = TreeConfig(output_format=OutputFormat.JSON())
    json_output = format_tree(tree, config_json, stats)
    assert '"name": "test_dir"' in json_output
    
    print("✓ Formatting works correctly")


def test_file_size_formatting():
    """Test human-readable file size formatting."""
    print("Testing file size formatting...")
    
    assert format_file_size(512) == "512B"
    assert format_file_size(1024) == "1.0K"
    assert format_file_size(1536) == "1.5K"
    assert format_file_size(1048576) == "1.0M"
    
    print("✓ File size formatting works correctly")


def test_configuration_immutability():
    """Test that TreeConfig is immutable."""
    print("Testing TreeConfig immutability...")
    
    config = TreeConfig(
        max_depth=5,
        show_hidden=True,
        show_size=True
    )
    
    # Test accessing values
    assert config.max_depth == 5
    assert config.show_hidden == True
    assert config.show_size == True
    
    # Test that we can't modify (frozen=True)
    try:
        config.max_depth = 10
        assert False, "TreeConfig should be immutable"
    except AttributeError:
        print("✓ TreeConfig is properly immutable")


def run_all_tests():
    """Run all functional programming tests."""
    print("Running tree-py functional programming tests...\n")
    
    test_file_info_immutability()
    test_tree_error_tagged_union()
    test_result_types()
    test_immutable_collections()
    test_filesystem_operations()
    test_tree_building()
    test_formatting()
    test_file_size_formatting()
    test_configuration_immutability()
    
    print("\n✅ All tests passed! Functional programming implementation is working correctly.")


if __name__ == "__main__":
    run_all_tests()
