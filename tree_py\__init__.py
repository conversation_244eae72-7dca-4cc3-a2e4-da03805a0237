"""
Tree-py: A functional programming implementation of the tree command using Expression library.

This package provides a purely functional approach to directory tree visualization,
leveraging immutable data structures, monadic error handling, and function composition.
"""

__version__ = "0.1.0"
__author__ = "Functional Programming Team"
__description__ = "Functional tree command implementation using Expression library"
