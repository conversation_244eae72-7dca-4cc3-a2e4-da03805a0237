"""
Functional filesystem operations using Result types for error handling.

This module provides pure functions for filesystem operations that return
Result types instead of raising exceptions, enabling railway-oriented programming.
"""

import os
import stat
from datetime import datetime
from pathlib import Path
from typing import Callable

from expression import Result, Ok, Error, pipe
from expression.collections import Block, Seq, seq

from .types import FileInfo, TreeError, TreeConfig


def safe_stat(path: Path) -> Result[os.stat_result, TreeError]:
    """Safely get file statistics, returning Result type."""
    try:
        return Ok(path.stat())
    except PermissionError:
        return Error(TreeError.PermissionDenied(str(path)))
    except FileNotFoundError:
        return Error(TreeError.NotFound(str(path)))
    except OSError as e:
        return Error(TreeError.IOError(str(path), str(e)))


def safe_listdir(path: Path) -> Result[Block[Path], TreeError]:
    """Safely list directory contents, returning Result type."""
    try:
        if not path.is_dir():
            return Error(TreeError.InvalidPath(f"{path} is not a directory"))
        
        entries = [path / entry for entry in os.listdir(path)]
        return Ok(Block.of_seq(entries))
    except PermissionError:
        return Error(TreeError.PermissionDenied(str(path)))
    except FileNotFoundError:
        return Error(TreeError.NotFound(str(path)))
    except OSError as e:
        return Error(TreeError.IOError(str(path), str(e)))


def get_file_permissions(mode: int) -> str:
    """Convert file mode to permission string."""
    permissions = []
    
    # File type
    if stat.S_ISDIR(mode):
        permissions.append('d')
    elif stat.S_ISLNK(mode):
        permissions.append('l')
    else:
        permissions.append('-')
    
    # Owner permissions
    permissions.append('r' if mode & stat.S_IRUSR else '-')
    permissions.append('w' if mode & stat.S_IWUSR else '-')
    permissions.append('x' if mode & stat.S_IXUSR else '-')
    
    # Group permissions
    permissions.append('r' if mode & stat.S_IRGRP else '-')
    permissions.append('w' if mode & stat.S_IWGRP else '-')
    permissions.append('x' if mode & stat.S_IXGRP else '-')
    
    # Other permissions
    permissions.append('r' if mode & stat.S_IROTH else '-')
    permissions.append('w' if mode & stat.S_IWOTH else '-')
    permissions.append('x' if mode & stat.S_IXOTH else '-')
    
    return ''.join(permissions)


def create_file_info(path: Path, stat_result: os.stat_result) -> FileInfo:
    """Create FileInfo from path and stat result."""
    return FileInfo(
        name=path.name,
        path=path,
        size=stat_result.st_size,
        modified_time=datetime.fromtimestamp(stat_result.st_mtime),
        is_directory=stat.S_ISDIR(stat_result.st_mode),
        is_symlink=stat.S_ISLNK(stat_result.st_mode),
        permissions=get_file_permissions(stat_result.st_mode),
        owner=None,  # Could be enhanced to get actual owner
        group=None   # Could be enhanced to get actual group
    )


def get_file_info(path: Path) -> Result[FileInfo, TreeError]:
    """Get file information for a given path."""
    return pipe(
        safe_stat(path),
        lambda stat_res: stat_res.map(lambda st: create_file_info(path, st))
    )


def should_include_file(file_info: FileInfo, config: TreeConfig) -> bool:
    """Determine if a file should be included based on configuration."""
    # Check hidden files
    if not config.show_hidden and file_info.name.startswith('.'):
        return False
    
    # Check include patterns
    if not config.include_patterns.is_empty():
        include_match = any(
            pattern in file_info.name 
            for pattern in config.include_patterns
        )
        if not include_match:
            return False
    
    # Check exclude patterns
    if not config.exclude_patterns.is_empty():
        exclude_match = any(
            pattern in file_info.name 
            for pattern in config.exclude_patterns
        )
        if exclude_match:
            return False
    
    return True


def filter_files(files: Block[FileInfo], config: TreeConfig) -> Block[FileInfo]:
    """Filter files based on configuration."""
    return pipe(
        files,
        lambda fs: fs.filter(lambda f: should_include_file(f, config))
    )


def sort_files(files: Block[FileInfo], config: TreeConfig) -> Block[FileInfo]:
    """Sort files based on configuration."""
    sort_key: Callable[[FileInfo], any]
    
    match config.sort_by:
        case "name":
            sort_key = lambda f: f.name.lower()
        case "size":
            sort_key = lambda f: f.size
        case "date":
            sort_key = lambda f: f.modified_time
        case _:
            sort_key = lambda f: f.name.lower()
    
    sorted_files = sorted(files, key=sort_key, reverse=config.reverse_sort)
    return Block.of_seq(sorted_files)
