"""
Functional output formatters for different tree representations.

This module provides pure functions for formatting tree structures into
various output formats using functional composition.
"""

import json
from typing import Dict, Any, List
from xml.etree.ElementTree import Element, tostring

from expression import pipe, compose
from expression.collections import Block, Seq, seq

from .types import TreeNode, TreeConfig, TreeStats, OutputFormat, FileInfo


def format_file_size(size: int, human_readable: bool = True) -> str:
    """Format file size in human-readable format."""
    if not human_readable:
        return str(size)
    
    units = ['B', 'K', 'M', 'G', 'T']
    size_float = float(size)
    unit_index = 0
    
    while size_float >= 1024 and unit_index < len(units) - 1:
        size_float /= 1024
        unit_index += 1
    
    if unit_index == 0:
        return f"{int(size_float)}{units[unit_index]}"
    else:
        return f"{size_float:.1f}{units[unit_index]}"


def get_tree_prefix(depth: int, is_last: bool, parent_prefixes: List[bool]) -> str:
    """Generate the tree prefix for a node."""
    if depth == 0:
        return ""
    
    prefix_parts = []
    
    # Add prefixes for parent levels
    for i, parent_is_last in enumerate(parent_prefixes):
        if i < len(parent_prefixes) - 1:
            prefix_parts.append("    " if parent_is_last else "│   ")
    
    # Add prefix for current level
    if is_last:
        prefix_parts.append("└── ")
    else:
        prefix_parts.append("├── ")
    
    return "".join(prefix_parts)


def format_file_info(file_info: FileInfo, config: TreeConfig) -> str:
    """Format file information based on configuration."""
    parts = [file_info.name]
    
    if config.show_size:
        size_str = format_file_size(file_info.size)
        parts.append(f"[{size_str}]")
    
    if config.show_permissions:
        parts.append(f"({file_info.permissions})")
    
    if config.show_date:
        date_str = file_info.modified_time.strftime("%Y-%m-%d %H:%M")
        parts.append(f"({date_str})")
    
    if config.show_owner and file_info.owner:
        parts.append(f"({file_info.owner})")
    
    return " ".join(parts)


def format_text_node(
    node: TreeNode, 
    config: TreeConfig, 
    parent_prefixes: List[bool] = None
) -> List[str]:
    """Format a tree node as text lines."""
    if parent_prefixes is None:
        parent_prefixes = []
    
    lines = []
    
    # Format current node
    prefix = get_tree_prefix(node.depth, node.is_last, parent_prefixes)
    file_display = format_file_info(node.info, config)
    lines.append(f"{prefix}{file_display}")
    
    # Format children
    if not node.children.is_empty():
        new_parent_prefixes = parent_prefixes + [node.is_last]
        for child in node.children:
            child_lines = format_text_node(child, config, new_parent_prefixes)
            lines.extend(child_lines)
    
    return lines


def format_text_tree(tree: TreeNode, config: TreeConfig, stats: TreeStats) -> str:
    """Format entire tree as text."""
    lines = format_text_node(tree, config)
    
    # Add statistics
    lines.append("")
    lines.append(f"{stats.total_directories} directories, {stats.total_files} files")
    
    if config.show_size:
        total_size = format_file_size(stats.total_size)
        lines.append(f"Total size: {total_size}")
    
    return "\n".join(lines)


def node_to_dict(node: TreeNode, config: TreeConfig) -> Dict[str, Any]:
    """Convert a tree node to dictionary representation."""
    result = {
        "name": node.info.name,
        "type": "directory" if node.info.is_directory else "file",
        "path": str(node.info.path)
    }
    
    if config.show_size:
        result["size"] = node.info.size
    
    if config.show_permissions:
        result["permissions"] = node.info.permissions
    
    if config.show_date:
        result["modified"] = node.info.modified_time.isoformat()
    
    if config.show_owner and node.info.owner:
        result["owner"] = node.info.owner
    
    if not node.children.is_empty():
        result["children"] = [
            node_to_dict(child, config) 
            for child in node.children
        ]
    
    return result


def format_json_tree(tree: TreeNode, config: TreeConfig, stats: TreeStats) -> str:
    """Format tree as JSON."""
    tree_dict = node_to_dict(tree, config)
    tree_dict["stats"] = {
        "total_directories": stats.total_directories,
        "total_files": stats.total_files,
        "total_size": stats.total_size
    }
    
    return json.dumps(tree_dict, indent=2)


def node_to_xml(node: TreeNode, config: TreeConfig) -> Element:
    """Convert a tree node to XML element."""
    element = Element("entry")
    element.set("name", node.info.name)
    element.set("type", "directory" if node.info.is_directory else "file")
    element.set("path", str(node.info.path))
    
    if config.show_size:
        element.set("size", str(node.info.size))
    
    if config.show_permissions:
        element.set("permissions", node.info.permissions)
    
    if config.show_date:
        element.set("modified", node.info.modified_time.isoformat())
    
    if not node.children.is_empty():
        for child in node.children:
            element.append(node_to_xml(child, config))
    
    return element


def format_xml_tree(tree: TreeNode, config: TreeConfig, stats: TreeStats) -> str:
    """Format tree as XML."""
    root = Element("tree")
    root.append(node_to_xml(tree, config))
    
    # Add stats
    stats_elem = Element("stats")
    stats_elem.set("directories", str(stats.total_directories))
    stats_elem.set("files", str(stats.total_files))
    stats_elem.set("total_size", str(stats.total_size))
    root.append(stats_elem)
    
    return tostring(root, encoding='unicode')


def format_html_tree(tree: TreeNode, config: TreeConfig, stats: TreeStats) -> str:
    """Format tree as HTML."""
    html_lines = [
        "<!DOCTYPE html>",
        "<html>",
        "<head>",
        "<title>Directory Tree</title>",
        "<style>",
        ".tree { font-family: monospace; }",
        ".directory { font-weight: bold; color: blue; }",
        ".file { color: black; }",
        "</style>",
        "</head>",
        "<body>",
        "<h1>Directory Tree</h1>",
        "<div class='tree'>"
    ]
    
    # Convert text format to HTML
    text_lines = format_text_node(tree, config)
    for line in text_lines:
        escaped_line = line.replace("<", "&lt;").replace(">", "&gt;")
        html_lines.append(f"<div>{escaped_line}</div>")
    
    html_lines.extend([
        "</div>",
        f"<p>{stats.total_directories} directories, {stats.total_files} files</p>",
        "</body>",
        "</html>"
    ])
    
    return "\n".join(html_lines)


def format_tree(
    tree: TreeNode, 
    config: TreeConfig, 
    stats: TreeStats
) -> str:
    """Format tree according to the specified output format."""
    match config.output_format:
        case OutputFormat(tag="text"):
            return format_text_tree(tree, config, stats)
        case OutputFormat(tag="json"):
            return format_json_tree(tree, config, stats)
        case OutputFormat(tag="xml"):
            return format_xml_tree(tree, config, stats)
        case OutputFormat(tag="html"):
            return format_html_tree(tree, config, stats)
        case _:
            return format_text_tree(tree, config, stats)


# Composed formatters for common use cases
format_simple_text = compose(
    lambda tree_stats: format_tree(
        tree_stats[0], 
        TreeConfig(output_format=OutputFormat.Text()), 
        tree_stats[1]
    )
)

format_detailed_json = compose(
    lambda tree_stats: format_tree(
        tree_stats[0],
        TreeConfig(
            show_size=True,
            show_permissions=True,
            show_date=True,
            output_format=OutputFormat.JSON()
        ),
        tree_stats[1]
    )
)
