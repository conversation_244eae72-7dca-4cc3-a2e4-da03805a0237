"""
Functional tree building using immutable data structures and composition.

This module provides pure functions for building directory trees using
functional programming principles and the Expression library.
"""

from pathlib import Path
from typing import Tuple

from expression import Result, Ok, Error, pipe, compose
from expression.collections import Block, Seq, seq

from .types import TreeNode, FileInfo, TreeError, TreeConfig, TreeStats
from .filesystem import (
    safe_listdir, 
    get_file_info, 
    filter_files, 
    sort_files
)


def build_tree_node(
    file_info: FileInfo, 
    children: Block[TreeNode], 
    depth: int, 
    is_last: bool
) -> TreeNode:
    """Create a tree node from file info and children."""
    return TreeNode(
        info=file_info,
        children=children,
        depth=depth,
        is_last=is_last
    )


def process_directory_entries(
    path: Path, 
    config: TreeConfig, 
    depth: int
) -> Result[Block[FileInfo], TreeError]:
    """Process directory entries and return filtered, sorted file info."""
    return pipe(
        safe_listdir(path),
        lambda entries_result: entries_result.bind(
            lambda entries: get_file_infos(entries)
        ),
        lambda infos_result: infos_result.map(
            lambda infos: pipe(
                infos,
                lambda fs: filter_files(fs, config),
                lambda fs: sort_files(fs, config)
            )
        )
    )


def get_file_infos(paths: Block[Path]) -> Result[Block[FileInfo], TreeError]:
    """Get file information for a collection of paths."""
    results = []
    errors = []
    
    for path in paths:
        match get_file_info(path):
            case Ok(file_info):
                results.append(file_info)
            case Error(error):
                errors.append(error)
    
    if errors:
        # Return first error for simplicity
        return Error(errors[0])
    
    return Ok(Block.of_seq(results))


def should_traverse_directory(
    file_info: FileInfo, 
    config: TreeConfig, 
    current_depth: int
) -> bool:
    """Determine if a directory should be traversed."""
    if not file_info.is_directory:
        return False
    
    if config.max_depth is not None and current_depth >= config.max_depth:
        return False
    
    if file_info.is_symlink and not config.follow_symlinks:
        return False
    
    return True


def build_tree_recursive(
    file_info: FileInfo,
    config: TreeConfig,
    depth: int,
    is_last: bool
) -> Result[TreeNode, TreeError]:
    """Recursively build a tree node and its children."""
    if not should_traverse_directory(file_info, config, depth):
        # Leaf node (file or directory we don't traverse)
        return Ok(build_tree_node(file_info, Block.empty(), depth, is_last))
    
    # Process directory
    return pipe(
        process_directory_entries(file_info.path, config, depth + 1),
        lambda entries_result: entries_result.bind(
            lambda entries: build_children_nodes(entries, config, depth + 1)
        ),
        lambda children_result: children_result.map(
            lambda children: build_tree_node(file_info, children, depth, is_last)
        )
    )


def build_children_nodes(
    file_infos: Block[FileInfo],
    config: TreeConfig,
    depth: int
) -> Result[Block[TreeNode], TreeError]:
    """Build child nodes for a directory."""
    if file_infos.is_empty():
        return Ok(Block.empty())
    
    children = []
    errors = []
    
    for i, file_info in enumerate(file_infos):
        is_last = i == len(file_infos) - 1
        
        match build_tree_recursive(file_info, config, depth, is_last):
            case Ok(node):
                children.append(node)
            case Error(error):
                errors.append(error)
    
    if errors:
        # Return first error for simplicity
        return Error(errors[0])
    
    return Ok(Block.of_seq(children))


def build_tree(root_path: Path, config: TreeConfig) -> Result[TreeNode, TreeError]:
    """Build a complete tree starting from the root path."""
    return pipe(
        get_file_info(root_path),
        lambda info_result: info_result.bind(
            lambda info: build_tree_recursive(info, config, 0, True)
        )
    )


def calculate_tree_stats(node: TreeNode) -> TreeStats:
    """Calculate statistics for a tree."""
    def count_nodes(current_node: TreeNode) -> Tuple[int, int, int]:
        """Count directories, files, and total size."""
        dirs = 1 if current_node.info.is_directory else 0
        files = 0 if current_node.info.is_directory else 1
        size = current_node.info.size
        
        for child in current_node.children:
            child_dirs, child_files, child_size = count_nodes(child)
            dirs += child_dirs
            files += child_files
            size += child_size
        
        return dirs, files, size
    
    total_dirs, total_files, total_size = count_nodes(node)
    
    return TreeStats(
        total_directories=total_dirs,
        total_files=total_files,
        total_size=total_size,
        errors_encountered=Block.empty()  # Could be enhanced to collect errors
    )


# Composition functions for building trees with different configurations
build_simple_tree = compose(
    lambda path: build_tree(path, TreeConfig()),
    lambda result: result.map(calculate_tree_stats)
)

build_detailed_tree = compose(
    lambda path: build_tree(
        path, 
        TreeConfig(
            show_size=True,
            show_permissions=True,
            show_date=True
        )
    ),
    lambda result: result.map(calculate_tree_stats)
)
